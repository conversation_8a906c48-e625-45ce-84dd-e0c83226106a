<?php
/**
 * Finální test drag & drop funkcionality pro Katalogy
 * Umístit do kořenového adresáře PrestaShop
 */

require_once(dirname(__FILE__) . "/config/config.inc.php");

echo "<!DOCTYPE html><html><head><meta charset='utf-8'>";
echo "<title>Test Drag & Drop - Katalogy</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.alert { padding: 15px; margin: 10px 0; border-radius: 4px; }
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-warning { background: #fff3cd; border: 1px solid #ffc107; color: #856404; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🧪 Finální Test Drag & Drop - Katalogy</h1>";

// 1. Test existence souborů
echo "<div class='test-section'>";
echo "<h2>1. ✅ Test existence klíčových souborů</h2>";

$files_to_check = [
    'AdminKatalogyController.php' => _PS_MODULE_DIR_ . 'katalogy/controllers/admin/AdminKatalogyController.php',
    'admin-drag-drop.js' => _PS_MODULE_DIR_ . 'katalogy/views/js/admin-drag-drop.js',
    'admin-katalogy.css' => _PS_MODULE_DIR_ . 'katalogy/views/css/admin-katalogy.css',
    'Katalog.php' => _PS_MODULE_DIR_ . 'katalogy/classes/Katalog.php'
];

foreach ($files_to_check as $name => $path) {
    if (file_exists($path)) {
        echo "<div class='alert alert-success'>✅ $name existuje</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ $name neexistuje: $path</div>";
    }
}
echo "</div>";

// 2. Test AdminKatalogyController metod
echo "<div class='test-section'>";
echo "<h2>2. 🔍 Test AdminKatalogyController metod</h2>";

if (file_exists(_PS_MODULE_DIR_ . 'katalogy/controllers/admin/AdminKatalogyController.php')) {
    $controller_content = file_get_contents(_PS_MODULE_DIR_ . 'katalogy/controllers/admin/AdminKatalogyController.php');
    
    $methods_to_check = [
        'ajaxProcessUpdatePositions' => 'AJAX metoda pro drag & drop',
        'processPosition' => 'Metoda pro šipky nahoru/dolů',
        'setMedia' => 'Metoda pro načítání JS/CSS',
        'displayPositionColumnContent' => 'Metoda pro zobrazení pozic se šipkami',
        'table_has_position = true' => 'Povolení pozic v PrestaShop'
    ];
    
    foreach ($methods_to_check as $method => $description) {
        if (strpos($controller_content, $method) !== false) {
            echo "<div class='alert alert-success'>✅ $description nalezena</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ $description nenalezena</div>";
        }
    }
} else {
    echo "<div class='alert alert-danger'>❌ AdminKatalogyController.php nenalezen</div>";
}
echo "</div>";

// 3. Test databáze a pozic
echo "<div class='test-section'>";
echo "<h2>3. 🗄️ Test databáze a pozic</h2>";

try {
    $sql = "SELECT id_katalog, title, position FROM " . _DB_PREFIX_ . "katalogy ORDER BY position ASC";
    $katalogy = Db::getInstance()->executeS($sql);
    
    if ($katalogy) {
        echo "<div class='alert alert-success'>✅ Nalezeno " . count($katalogy) . " katalogů</div>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Název</th><th>Pozice</th></tr>";
        
        foreach ($katalogy as $katalog) {
            echo "<tr>";
            echo "<td>" . $katalog['id_katalog'] . "</td>";
            echo "<td>" . htmlspecialchars($katalog['title']) . "</td>";
            echo "<td style='text-align: center; font-weight: bold;'>" . $katalog['position'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ Žádné katalogy nenalezeny</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Chyba databáze: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 4. Test AJAX URL
echo "<div class='test-section'>";
echo "<h2>4. 🌐 Test AJAX URL</h2>";

$admin_dir = '';
$possible_admin_dirs = ['admin', 'admin123', 'admin456', 'admin789', 'adminxyz'];

foreach ($possible_admin_dirs as $dir) {
    if (is_dir(_PS_ROOT_DIR_ . '/' . $dir) && file_exists(_PS_ROOT_DIR_ . '/' . $dir . '/index.php')) {
        $admin_dir = $dir;
        break;
    }
}

if ($admin_dir) {
    $ajax_url = "/$admin_dir/index.php?controller=AdminKatalogy&ajax=1&action=updatePositions";
    echo "<div class='alert alert-success'>✅ Admin adresář nalezen: /$admin_dir/</div>";
    echo "<div class='alert alert-info'>📡 AJAX URL: <code>$ajax_url</code></div>";
} else {
    echo "<div class='alert alert-warning'>⚠️ Admin adresář nenalezen</div>";
}
echo "</div>";

// 5. Test JavaScript
echo "<div class='test-section'>";
echo "<h2>5. 📜 Test JavaScript konfigurace</h2>";

if (file_exists(_PS_MODULE_DIR_ . 'katalogy/views/js/admin-drag-drop.js')) {
    $js_content = file_get_contents(_PS_MODULE_DIR_ . 'katalogy/views/js/admin-drag-drop.js');
    
    $js_features = [
        'sortable' => 'jQuery UI Sortable inicializace',
        'ajaxProcessUpdatePositions' => 'AJAX volání pro pozice',
        'hasError' => 'Správné zpracování odpovědi',
        'showSuccessMessage' => 'Zobrazení úspěšných zpráv',
        'updateTablePositions' => 'Aktualizace pozic bez reload'
    ];
    
    foreach ($js_features as $feature => $description) {
        if (strpos($js_content, $feature) !== false) {
            echo "<div class='alert alert-success'>✅ $description</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ $description nenalezena</div>";
        }
    }
} else {
    echo "<div class='alert alert-danger'>❌ admin-drag-drop.js nenalezen</div>";
}
echo "</div>";

// 6. Návod k testování
echo "<div class='test-section'>";
echo "<h2>6. 📋 Návod k testování</h2>";
echo "<div class='alert alert-info'>";
echo "<h4>Postup testování drag & drop:</h4>";
echo "<ol>";
echo "<li><strong>Přejděte do administrace</strong> → Katalogy</li>";
echo "<li><strong>Zkontrolujte</strong>, že vidíte sloupec 'Pozice' se šipkami ↑↓</li>";
echo "<li><strong>Otevřete Developer Tools</strong> (F12) → Console</li>";
echo "<li><strong>Zkuste přetáhnout</strong> řádek na jinou pozici</li>";
echo "<li><strong>Sledujte console</strong> pro debug zprávy</li>";
echo "<li><strong>Zkontrolujte</strong>, že se pozice uložily (refresh stránky)</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

// 7. Debug informace
echo "<div class='test-section'>";
echo "<h2>7. 🔧 Debug informace</h2>";
echo "<div class='alert alert-info'>";
echo "<strong>PrestaShop verze:</strong> " . _PS_VERSION_ . "<br>";
echo "<strong>PHP verze:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Modul cesta:</strong> " . _PS_MODULE_DIR_ . "katalogy/<br>";
echo "<strong>Admin token:</strong> " . Tools::getAdminTokenLite('AdminKatalogy') . "<br>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-success'>";
echo "<h3>🎉 Test dokončen!</h3>";
echo "<p>Pokud všechny testy prošly, drag & drop by měl fungovat. Pokud ne, zkontrolujte chybové logy serveru.</p>";
echo "</div>";

echo "</body></html>";
?>
