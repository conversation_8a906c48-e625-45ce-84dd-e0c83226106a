<?php
/**
 * Admin Controller for Katalogy Module - OPRAVA POSITION SYSTÉMU
 */

require_once(_PS_MODULE_DIR_ . 'katalogy/classes/Katalog.php');

class AdminKatalogyController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'katalogy';
        $this->className = 'Katalog';
        $this->lang = false;
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->identifier = 'id_katalog';

        parent::__construct();

        $this->fields_list = [
            'id_katalog' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'image' => [
                'title' => $this->l('Obrázek'),
                'align' => 'center',
                'image' => 'katalogy',
                'orderby' => false,
                'search' => false,
                'class' => 'fixed-width-xs'
            ],
            'title' => [
                'title' => $this->l('Název'),
                'width' => 'auto'
            ],
            'description' => [
                'title' => $this->l('Popis'),
                'width' => 'auto',
                'maxlength' => 100
            ],
            'is_new' => [
                'title' => $this->l('Nový'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-xs'
            ],
            'position' => [
                'title' => $this->l('Pozice'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'position' => 'position',      // KLÍČOVÉ!
                'orderby' => false,           // Zakáže řazení kliknutím
                'search' => false,            // Zakáže vyhledávání
                'remove_onclick' => true      // Odstraní onclick
            ],
            'active' => [
                'title' => $this->l('Aktivní'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-xs'
            ],
            'date_add' => [
                'title' => $this->l('Vytvořeno'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            ]
        ];

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Smazat vybrané'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Smazat vybrané položky?')
            ]
        ];

        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Katalog'),
                'icon' => 'icon-folder-open'
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Název'),
                    'name' => 'title',
                    'required' => true,
                    'size' => 50
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Popis'),
                    'name' => 'description',
                    'rows' => 5,
                    'cols' => 50
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Obrázek'),
                    'name' => 'image',
                    'desc' => $this->l('Nahrajte náhledový obrázek katalogu')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('URL katalogu'),
                    'name' => 'file_url',
                    'size' => 100,
                    'desc' => $this->l('Zadejte URL pro stažení katalogu')
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Soubor katalogu'),
                    'name' => 'catalog_file',
                    'desc' => $this->l('Nebo nahrajte soubor katalogu')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Nový katalog'),
                    'name' => 'is_new',
                    'values' => [
                        [
                            'id' => 'is_new_on',
                            'value' => 1,
                            'label' => $this->l('Ano')
                        ],
                        [
                            'id' => 'is_new_off',
                            'value' => 0,
                            'label' => $this->l('Ne')
                        ]
                    ]
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Pozice'),
                    'name' => 'position',
                    'size' => 5,
                    'desc' => $this->l('Pořadí zobrazení (nižší číslo = vyšší pozice)')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Aktivní'),
                    'name' => 'active',
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Ano')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Ne')
                        ]
                    ]
                ]
            ],
            'submit' => [
                'title' => $this->l('Uložit')
            ]
        ];

        // KRITICKÉ NASTAVENÍ PRO PRESTASHOP POSITION SYSTEM
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';
        
        // ⭐ KLÍČOVÉ ŘÁDKY PRO POSITION FUNKČNOST:
        $this->position_identifier = 'id_katalog';
        $this->_use_found_rows = false;
        $this->can_import = true;
        $this->allow_export = true;
        
        // ⭐ FORCE POSITION COLUMN
        $this->list_id = 'katalogy';
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';
    }

    /**
     * ⭐ OVERRIDE: Přidání pozičních šipek do každého řádku
     */
    public function renderList()
    {
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        
        // ⭐ FORCE přidání position actions
        $this->addRowActionSkipList('position', ['position']);
        
        return parent::renderList();
    }

    /**
     * ⭐ OVERRIDE: Přídání position buněk s šipkami
     */
    protected function displayPositionColumnContent($value, $tr)
    {
        if (isset($tr['id_' . $this->table])) {
            $id = $tr['id_' . $this->table];
            $position = $tr['position'];
            
            return '
                <div class="btn-group-vertical">
                    <a class="btn btn-default" href="' . 
                    self::$currentIndex . '&' . $this->identifier . '=' . $id . 
                    '&way=1&position&token=' . $this->token . '" title="' . $this->l('Nahoru') . '">
                        <i class="icon-chevron-up"></i>
                    </a>
                    <span style="display: block; text-align: center; line-height: 20px; font-weight: bold;">' . $position . '</span>
                    <a class="btn btn-default" href="' . 
                    self::$currentIndex . '&' . $this->identifier . '=' . $id . 
                    '&way=0&position&token=' . $this->token . '" title="' . $this->l('Dolů') . '">
                        <i class="icon-chevron-down"></i>
                    </a>
                </div>';
        }
        
        return $value;
    }

    /**
     * ⭐ KLÍČOVÁ METODA: updatePositions pro drag & drop
     */
    public function ajaxProcessUpdatePositions()
    {
        error_log("=== PRESTASHOP POSITION UPDATE ===");
        error_log("POST: " . print_r($_POST, true));
        error_log("GET: " . print_r($_GET, true));
        
        $positions = Tools::getValue($this->table);
        
        if (!$positions || !is_array($positions)) {
            error_log("❌ No positions data");
            die(json_encode(['hasError' => true, 'errors' => ['No positions data']]));
        }

        try {
            Db::getInstance()->execute('START TRANSACTION');
            $updated_count = 0;
            
            foreach ($positions as $position => $value) {
                error_log("Processing: position=$position, value=$value");
                
                // Extrakce ID z různých formátů
                $katalog_id = null;
                
                if (preg_match('/_(\d+)$/', $value, $matches)) {
                    $katalog_id = (int)$matches[1];
                } elseif (is_numeric($value)) {
                    $katalog_id = (int)$value;
                }
                
                if ($katalog_id > 0) {
                    $db_position = (int)$position + 1; // 0-based to 1-based
                    
                    $sql = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                           SET `position` = ' . (int)$db_position . ',
                               `date_upd` = NOW()
                           WHERE `id_katalog` = ' . (int)$katalog_id;
                    
                    if (Db::getInstance()->execute($sql)) {
                        error_log("✅ Updated katalog $katalog_id to position $db_position");
                        $updated_count++;
                    } else {
                        error_log("❌ Failed to update katalog $katalog_id");
                    }
                } else {
                    error_log("❌ Could not extract ID from: $value");
                }
            }
            
            if ($updated_count > 0) {
                Db::getInstance()->execute('COMMIT');
                error_log("=== SUCCESS: $updated_count positions updated ===");
                
                die(json_encode(['hasError' => false, 'updated' => $updated_count]));
            } else {
                throw new Exception("No positions were updated");
            }
            
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            error_log("❌ ERROR: " . $e->getMessage());
            die(json_encode(['hasError' => true, 'errors' => [$e->getMessage()]]));
        }
    }

    /**
     * ⭐ KLÍČOVÁ METODA: processPosition pro šipky ↑↓
     */
    public function processPosition()
    {
        error_log("=== PROCESS POSITION START ===");
        
        if (!$this->loadObject(true)) {
            error_log("❌ Could not load object");
            return false;
        }

        $way = (int)Tools::getValue('way');
        $katalog = $this->object;
        $current_position = (int)$katalog->position;
        
        error_log("ProcessPosition: way=$way, current_position=$current_position, katalog_id=" . $katalog->id);
        
        if ($way) {
            // Nahoru (snížit pozici)
            $new_position = max(1, $current_position - 1);
        } else {
            // Dolů (zvýšit pozici)
            $new_position = $current_position + 1;
        }

        if ($current_position == $new_position) {
            error_log("⚠️ No position change needed");
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
            return true;
        }

        try {
            Db::getInstance()->execute('START TRANSACTION');
            
            // Najdi katalog na cílové pozici
            $swap_sql = 'SELECT `id_katalog`, `position` FROM `' . _DB_PREFIX_ . 'katalogy` 
                        WHERE `position` = ' . (int)$new_position . ' 
                        AND `id_katalog` != ' . (int)$katalog->id . ' 
                        LIMIT 1';
            $swap_katalog = Db::getInstance()->getRow($swap_sql);
            
            if ($swap_katalog) {
                // Prohoď pozice
                $update1 = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                           SET `position` = ' . (int)$current_position . ',
                               `date_upd` = NOW()
                           WHERE `id_katalog` = ' . (int)$swap_katalog['id_katalog'];
                
                if (!Db::getInstance()->execute($update1)) {
                    throw new Exception('Failed to update swap katalog position');
                }
                
                error_log("✅ Swapped katalog " . $swap_katalog['id_katalog'] . " from position $new_position to $current_position");
            }
            
            // Aktualizuj pozici aktuálního katalogu
            $update2 = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                       SET `position` = ' . (int)$new_position . ',
                           `date_upd` = NOW()
                       WHERE `id_katalog` = ' . (int)$katalog->id;
            
            if (Db::getInstance()->execute($update2)) {
                Db::getInstance()->execute('COMMIT');
                error_log("✅ Moved katalog " . $katalog->id . " from position $current_position to $new_position");
                
                $this->confirmations[] = $this->l('Pozice byla úspěšně změněna.');
                $this->redirect_after = self::$currentIndex . '&token=' . $this->token . '&conf=5';
                return true;
            } else {
                throw new Exception('Failed to update katalog position');
            }
            
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            error_log("❌ ProcessPosition error: " . $e->getMessage());
            $this->errors[] = $this->l('Chyba při změně pozice: ') . $e->getMessage();
            return false;
        }
    }

    public function renderForm()
    {
        if (isset($this->object) && $this->object->id) {
            $katalog = new Katalog($this->object->id);
            
            if ($katalog->image) {
                $image_url = _MODULE_DIR_ . 'katalogy/views/img/katalogy/' . $katalog->image;
                $current_image_input = [
                    'type' => 'html',
                    'label' => $this->l('Aktuální obrázek'),
                    'name' => 'current_image_display',
                    'html_content' => '<img src="' . $image_url . '" alt="Aktuální obrázek" style="max-width: 200px; max-height: 200px;" />' .
                                     '<input type="hidden" name="existing_image" value="' . $katalog->image . '" />'
                ];
                array_splice($this->fields_form['input'], 2, 0, [$current_image_input]);
            }

            if ($katalog->file_path) {
                $file_url = _MODULE_DIR_ . 'katalogy/files/' . $katalog->file_path;
                $current_file_input = [
                    'type' => 'html',
                    'label' => $this->l('Aktuální soubor'),
                    'name' => 'current_file_display',
                    'html_content' => '<a href="' . $file_url . '" target="_blank" class="btn btn-default">' . 
                                     $this->l('Stáhnout aktuální soubor') . '</a>' .
                                     '<input type="hidden" name="existing_file_path" value="' . $katalog->file_path . '" />'
                ];
                array_splice($this->fields_form['input'], 5, 0, [$current_file_input]);
            }
        }
        return parent::renderForm();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submit' . $this->table)) {
            $id = (int)Tools::getValue('id_katalog');
            if ($id > 0) {
                return $this->processUpdate();
            } else {
                return $this->processAdd();
            }
        }
        return parent::postProcess();
    }

    public function processAdd()
    {
        $katalog = new Katalog();
        $this->copyFromPost($katalog, $this->table);
        
        if (empty($katalog->position) || $katalog->position == 0) {
            $katalog->position = $this->getNextPosition();
        }

        $katalog->date_add = date('Y-m-d H:i:s');
        $katalog->date_upd = date('Y-m-d H:i:s');

        if ($katalog->save()) {
            $this->handleFileUploads($katalog);
            $this->confirmations[] = $this->l('Katalog byl úspěšně přidán.');
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
        } else {
            $this->errors[] = $this->l('Chyba při ukládání katalogu.');
        }
    }

    public function processUpdate()
    {
        $id = (int)Tools::getValue('id_katalog');
        $katalog = new Katalog($id);

        if (!Validate::isLoadedObject($katalog)) {
            $this->errors[] = $this->l('Katalog nebyl nalezen.');
            return false;
        }

        $original_image = $katalog->image;
        $original_file_path = $katalog->file_path;
        $original_position = $katalog->position;

        $this->copyFromPost($katalog, $this->table);
        $katalog->id = $id;
        $katalog->id_katalog = $id;

        if (empty($_FILES['image']['tmp_name']) && Tools::getValue('existing_image')) {
            $katalog->image = Tools::getValue('existing_image');
        }

        if (empty($_FILES['catalog_file']['tmp_name']) && Tools::getValue('existing_file_path')) {
            $katalog->file_path = Tools::getValue('existing_file_path');
        }

        if (empty($katalog->position) || $katalog->position == 0) {
            $katalog->position = $original_position;
        }

        $katalog->date_upd = date('Y-m-d H:i:s');

        if ($katalog->update()) {
            $this->handleFileUploads($katalog, $original_image, $original_file_path);
            $this->confirmations[] = $this->l('Katalog byl úspěšně upraven.');
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
        } else {
            $this->errors[] = $this->l('Chyba při ukládání katalogu.');
        }
    }

    private function handleFileUploads($katalog, $original_image = null, $original_file_path = null)
    {
        $updated = false;

        if (isset($_FILES['image']) && $_FILES['image']['size'] > 0 && $_FILES['image']['error'] == 0) {
            $image_name = $katalog->id . '_' . time() . '.jpg';
            $upload_dir = _PS_MODULE_DIR_ . 'katalogy/views/img/katalogy/';

            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image_name)) {
                if ($original_image && $original_image != $image_name && file_exists($upload_dir . $original_image)) {
                    unlink($upload_dir . $original_image);
                }
                $katalog->image = $image_name;
                $updated = true;
            }
        }

        if (isset($_FILES['catalog_file']) && $_FILES['catalog_file']['size'] > 0 && $_FILES['catalog_file']['error'] == 0) {
            $file_extension = pathinfo($_FILES['catalog_file']['name'], PATHINFO_EXTENSION);
            $file_name = $katalog->id . '_catalog_' . time() . '.' . $file_extension;
            $upload_dir = _PS_MODULE_DIR_ . 'katalogy/files/';

            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            if (move_uploaded_file($_FILES['catalog_file']['tmp_name'], $upload_dir . $file_name)) {
                if ($original_file_path && $original_file_path != $file_name && file_exists($upload_dir . $original_file_path)) {
                    unlink($upload_dir . $original_file_path);
                }
                $katalog->file_path = $file_name;
                $updated = true;
            }
        }

        if ($updated) {
            $katalog->update();
        }
    }

    private function getNextPosition()
    {
        $sql = 'SELECT MAX(`position`) as max_pos FROM `' . _DB_PREFIX_ . 'katalogy`';
        $result = Db::getInstance()->getRow($sql);
        return (int)$result['max_pos'] + 1;
    }

    /**
     * ⭐ FORCE správné řazení
     */
    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        // VŽDY řaď podle pozice
        $order_by = 'position';
        $order_way = 'ASC';

        return parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);
    }

    /**
     * ⭐ FORCE position nastavení
     */
    public function init()
    {
        // FORCE pozice
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';
        
        parent::init();
    }
}